"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_app = require("../../stores/app.js");
if (!Array) {
  const _component_CustomNavbar = common_vendor.resolveComponent("CustomNavbar");
  const _easycom_common_header2 = common_vendor.resolveComponent("common-header");
  const _component_CustomTabbar = common_vendor.resolveComponent("CustomTabbar");
  (_component_CustomNavbar + _easycom_common_header2 + _component_CustomTabbar)();
}
const _easycom_common_header = () => "../../components/common-header/common-header.js";
if (!Math) {
  (_easycom_common_header + Commonscenarios)();
}
const Commonscenarios = () => "./Commonscenarios.js";
const _sfc_main = {
  __name: "home",
  setup(__props) {
    const {
      t
    } = common_vendor.useI18n();
    const appStores = stores_app.appStore();
    common_vendor.computed(() => stores_app.appStore.lang);
    common_vendor.ref(t("home.aiLanguageAssistant"));
    common_vendor.onShow(() => {
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          title: common_vendor.unref(t)("home.aiLanguageAssistant"),
          changeLang: true,
          fixed: true
        }),
        b: common_vendor.t(_ctx.$t("home.usedRecords")),
        c: common_vendor.f(9, (item, index, i0) => {
          return {
            a: common_vendor.f(5, (i, k1, i1) => {
              return {
                a: common_vendor.t(i <= Math.round(item.score) ? "★" : "☆"),
                b: i
              };
            }),
            b: index
          };
        }),
        d: common_vendor.t(_ctx.$t("home.wordLearning")),
        e: common_vendor.n(common_vendor.unref(appStores).lang == "zh-Ug" ? "ug" : ""),
        f: common_vendor.p({
          current: "home"
        })
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-07e72d3c"]]);
wx.createPage(MiniProgramPage);
