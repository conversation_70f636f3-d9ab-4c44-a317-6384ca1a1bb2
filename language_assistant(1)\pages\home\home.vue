<template>
	<CustomNavbar :title="(t('home.aiLanguageAssistant'))" changeLang :fixed="true" />
	<!-- 首页 -->
	<view class="page-container" :class="appStores.lang=='zh-Ug'?'ug':''">
		<!-- 自定义导航栏 -->
		<view class="main-content">
			<!-- 顶部窗口 -->
			<common-header></common-header>
			<view class="scene-section">
				<!-- 常用场景Tab -->
				<Commonscenarios />
				<!-- 我用过的记录 -->
				<view class="recommend-list">
					<view class="recommend-name">{{ $t('home.usedRecords') }}</view>
					<view class="recommend-list-info">
						<view class="recommend-item" v-for="(item, index) in 9" :key="index">
							<view class="recommend-img"></view>
							<view class="recommend-info">
								<view class="recommend-title">在火车站买火车票时对话学习</view>
								<view class="recommend-time">{{ $t('home.wordLearning') }}</view>
								<view class="recommend-time">2025年1月20日 19:23</view>
								<view class="recommend-meta">
									<text class="score">4.5</text>
									<text v-for="i in 5" :key="i"
										class="star">{{ i <= Math.round(item.score) ? '★' : '☆' }}</text>
									<text class="count">(124)</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
	<CustomTabbar current="home" />
</template>

<script setup>
	import {
		ref,
		watch,
		onMounted,
		computed
	} from 'vue';

	import {
		onShow,
	} from '@dcloudio/uni-app'
	import {
		appStore,
	} from '@/stores/app.js'
	import Commonscenarios from '../home/<USER>';
	import {
		useI18n
	} from 'vue-i18n';
	const {
		t
	} = useI18n();
	const appStores = appStore()
	const lang = computed(() => appStore.lang)
	const title = ref(t('home.aiLanguageAssistant'));
	onShow(() => {});
</script>

<style lang="scss" scoped>
	.main-content {
		/* 	flex: 1;
	padding: 24rpx 0 0 0; */
		overflow-y: auto;
		/* margin-top: 200rpx; */
		// 为固定导航栏预留空间，大约100px的高度
		padding-top: 100px;
	}

	.scene-section {
		width: 686rpx;
		height: 578rpx;
		padding: 24rpx 16rpx;
		margin: 0 20rpx 24rpx 20rpx;

	}

	.recommend-list {
		margin-top: 30rpx;
		padding-bottom: 24rpx;
	}

	.recommend-name {
		font-size: 44rpx;
		color: #727972;
		margin-bottom: 16rpx;
		margin-left: 18px;
	}

	.recommend-list-info {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 14rpx;
		padding: 16rpx;
		/* 	direction: ltr;
	unicode-bidi: isolate; */
	}

	.recommend-item {
		display: flex;
		border-radius: 22rpx;
		border: 1px solid rgb(236, 229, 242);
		width: 330rpx;
		height: 115rpx;
	}

	.recommend-img {
		width: 92rpx;
		height: 100rpx;
		border-radius: 16rpx;
		background: #eaeaea;
		margin: 4px;
	}

	.recommend-info {
		margin-top: 8rpx;
	}

	.recommend-title {
		font-size: 8px;
		font-weight: 600;
		line-height: 15px;
		width: 100px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.recommend-time {
		font-size: 8px;
		color: #666;
		margin-bottom: 2px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		width: 100px;
	}

	.recommend-meta {
		display: flex;
		align-items: center;
		font-size: 18rpx;
		color: #ffb400;
	}

	.recommend-meta .score {
		color: #006397;
		font-weight: bold;
		margin-right: 4rpx;
	}

	.recommend-meta .count {
		color: #888;
		margin-left: 4rpx;
	}

	.iconfont {
		font-style: normal;
		font-size: 28rpx;
		color: #006397;
	}
</style>