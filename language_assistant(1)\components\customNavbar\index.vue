<template>
	<!-- 自定义导航栏容器，高度根据设备动态计算 -->
	<view class="navigation-bar" :class="{ 'navigation-bar--fixed': fixed }" :style="{ height: navbarHeight}">
		<!-- 导航栏内部主体，动态设置高度、顶部边距和宽度 -->
		<view class="navigation-bar__inner"
			:style="{ height: innerHeight + 'px',top:innerTop+'px',width:innerWidth+'px'}">
			<!-- 左侧区域：包含返回按钮和语言切换按钮 -->
			<view class="navigation-bar__inner-left" :style="{ width: leftWidth + 'px'}">
				<block v-if="back || changeLang">
					<!-- 语言切换按钮 -->
					<block v-if="changeLang">
						<view class="change-lang ug-font" @tap="changeLanguage">
							<!-- 维语按钮 -->
							<view class="change-lang-item" style="padding: 0 8rpx 0 12rpx;"
								:class="appStores.lang=='zh-Ug'?'lang-selected':''">ئۇ</view>
							<!-- 中文按钮 -->
							<view class="change-lang-item" style="padding: 0 8rpx 0 4rpx;"
								:class="appStores.lang=='zh-Hans'?'lang-selected':''">中
							</view>
						</view>
					</block>
					<!-- 返回按钮 -->
					<block v-if="back">
						<view @tap="previous">
							<uni-icons type="left" size="24" :color="color"></uni-icons>
						</view>
					</block>
				</block>
			</view>
			<!-- 中间区域：显示标题 -->
			<view class="navigation-bar__inner-center ug-font" :style="{ width: centerWidth + 'px' }">
				<block v-if="title">
					<text :style="{ color: color }">{{ title }}</text>
				</block>
			</view>
		</view>
	</view>
</template>

<script setup>
	// 引入Vue相关API
	import {
		computed,
		ref,
		onMounted
	} from 'vue'
	// 引入全局状态管理
	import {
		appStore
	} from '@/stores/app.js'
	// 引入国际化相关
	import {
		useI18n
	} from 'vue-i18n';
	// 获取国际化实例
	const {
		locale
	} = useI18n();
	// 获取全局应用状态
	const appStores = appStore()

	// 定义组件属性
	const props = defineProps({
		title: String, // 导航栏标题
		background: {
			type: String,
			default: "" // 背景色，默认为空
		},
		color: {
			type: String,
			default: '#006397' // 字体颜色，默认为蓝色
		},
		back: Boolean, // 是否显示返回按钮
		changeLang: {
			type: Boolean,
			default: false // 是否显示语言切换按钮
		},
		fixed: {
			type: Boolean,
			default: false // 是否固定定位
		}
	})

	// 响应式数据定义
	const primaryColor = ref(props.color) // 主色调
	const bg = ref('') // 背景色
	const statusBarH = ref(0); // 状态栏高度
	const leftWidth = ref(0); // 左侧区域宽度
	const innerHeight = ref(0); // 导航栏内部高度
	const innerTop = ref(0); // 内部顶部距离
	const innerWidth = ref(0) // 内部宽度
	const centerWidth = ref(0); // 中间区域宽度

	// 计算导航栏总高度（响应式）
	const navbarHeight = computed(() => {
		// 获取设备信息
		const systemInfo = uni.getDeviceInfo();
		// console.log('当前平台:', systemInfo);
		// toLowerCase（）是字符串转小写的方法
		// includes() 用于判断某个字符串是否包含制定内容
		// 判断是否为iOS设备
		const isIOS = (systemInfo.platform && systemInfo.platform.toLowerCase() === 'ios') || (systemInfo.system &&
			systemInfo.system.toLowerCase().includes('ios'));
		// iOS和安卓高度差异设置
		let fixedHeight = isIOS ? 42 : 40 // iOS为42px，安卓为40x

		// APP端额外高度调整
		//#ifdef APP-PLUS
		fixedHeight += 30
		//#endif

		// 返回计算后的高度字符串
		return appStores.statusBarHeight ? `${statusBarH.value + fixedHeight + appStores.statusBarHeight}px` :
			`${statusBarH.value + fixedHeight}px`;
	});

	// 返回上一页方法
	const previous = () => {
		uni.navigateBack({
			delta: 1
		});
	}

	// 切换语言方法
	const changeLanguage = () => {
		// 切换中/维文
		const langeValue = appStores.lang == 'zh-Hans' ? 'zh-Ug' : 'zh-Hans';
		// 设置当前语言
		locale.value = langeValue;
		// 保存到本地存储
		uni.setStorageSync('language', langeValue);
		// 更新全局状态
		appStores.lang = langeValue;
	}

	// 组件挂载后的初始化逻辑
	onMounted(() => {
		// 获取窗口信息
		const res = uni.getWindowInfo()
		// 设置内部宽度
		innerWidth.value = res.windowWidth - leftWidth.value - 25

		// 微信小程序端适配
		// #ifdef MP-WEIXIN
		// 获取胶囊按钮信息
		const rect = uni.getMenuButtonBoundingClientRect();
		// 设置导航栏内部高度
		innerHeight.value = rect.height + 6;
		// 设置内部顶部距离
		innerTop.value = rect.top - statusBarH.value - 3;

		// 计算左侧宽度（考虑胶囊按钮位置）
		leftWidth.value = props.leftExtraWidth ? res.windowWidth - rect.left + props.leftExtraWidth - 48 : res
			.windowWidth - rect.left - 48;
		// 计算中间区域宽度
		centerWidth.value = res.windowWidth - leftWidth.value - rect.width;
		//#endif

		// APP端适配
		//#ifdef APP-PLUS
		const left = 12
		// 设置导航栏内部高度
		innerHeight.value = 32;
		// 设置内部顶部距离
		innerTop.value = statusBarH.value + 30 // 计算左侧宽度
		leftWidth.value = props.leftExtraWidth ? res.windowWidth - left + props.leftExtraWidth - 48 : res
			.windowWidth - left - 48;
		// 计算中间区域宽度
		centerWidth.value = res.windowWidth - leftWidth.value
		//#endif
	});
</script>

<style lang="scss">
	// 使用v-bind绑定主色调变量
	$borer-color: v-bind(primaryColor);

	// 导航栏主容器样式
	.navigation-bar {
		// 固定定位样式
		&--fixed {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			z-index: 1000;
		}

		// 导航栏内部主体样式
		&__inner {
			position: relative;
			background-color: #F1F4F9; // 浅灰色背景
			margin-left: 16rpx; // 左边距
			display: flex; // 弹性布局
			align-items: center; // 垂直居中
			border-radius: 36rpx; // 圆角
			padding-left: 20rpx; // 左内边距

			// 左侧区域样式
			&-left {
				height: 32px; // 固定高度
				display: flex; // 弹性布局
				align-items: center; // 垂直居中
				// background-color: red; // 调试用背景色（已注释）
			}

			// 中间区域样式
			&-center {
				font-size: 16px; // 字体大小
				text-align: center; // 文字居中
				position: relative; // 相对定位
				display: flex; // 弹性布局
				align-items: center; // 垂直居中
				justify-content: center; // 水平居中
				font-weight: bold; // 字体加粗
				height: 100%; // 占满高度
			}
		}

		// 语言切换按钮样式
		.change-lang {
			align-items: center; // 垂直居中
			width: 110rpx; // 固定宽度
			display: flex; // 弹性布局
			border-radius: 40rpx; // 圆角
			border: 2rpx solid $borer-color; // 边框
			justify-content: space-between; // 两端对齐
			// padding: 4rpx 8rpx; // 内边距（已注释）
			height: 40rpx; // 固定高度
			font-weight: 600; // 字体粗细
			overflow: hidden; // 隐藏溢出内容
		}

		// 选中语言的高亮样式
		.lang-selected {
			color: #fff; // 白色文字
			background-color: $borer-color; // 主色调背景
		}
	}
</style>