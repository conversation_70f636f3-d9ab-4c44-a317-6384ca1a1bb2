"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_app = require("../../stores/app.js");
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  _easycom_uni_icons2();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
const _sfc_main = {
  __name: "index",
  props: {
    title: String,
    // 导航栏标题
    background: {
      type: String,
      default: ""
      // 背景色，默认为空
    },
    color: {
      type: String,
      default: "#006397"
      // 字体颜色，默认为蓝色
    },
    back: Boolean,
    // 是否显示返回按钮
    changeLang: {
      type: Boolean,
      default: false
      // 是否显示语言切换按钮
    },
    fixed: {
      type: <PERSON><PERSON>an,
      default: false
      // 是否固定定位
    }
  },
  setup(__props) {
    common_vendor.useCssVars((_ctx) => ({
      "5ab80855": primaryColor.value
    }));
    const {
      locale
    } = common_vendor.useI18n();
    const appStores = stores_app.appStore();
    const props = __props;
    const primaryColor = common_vendor.ref(props.color);
    common_vendor.ref("");
    const statusBarH = common_vendor.ref(0);
    const leftWidth = common_vendor.ref(0);
    const innerHeight = common_vendor.ref(0);
    const innerTop = common_vendor.ref(0);
    const innerWidth = common_vendor.ref(0);
    const centerWidth = common_vendor.ref(0);
    const navbarHeight = common_vendor.computed(() => {
      const systemInfo = common_vendor.index.getDeviceInfo();
      const isIOS = systemInfo.platform && systemInfo.platform.toLowerCase() === "ios" || systemInfo.system && systemInfo.system.toLowerCase().includes("ios");
      let fixedHeight = isIOS ? 42 : 40;
      return appStores.statusBarHeight ? `${statusBarH.value + fixedHeight + appStores.statusBarHeight}px` : `${statusBarH.value + fixedHeight}px`;
    });
    const previous = () => {
      common_vendor.index.navigateBack({
        delta: 1
      });
    };
    const changeLanguage = () => {
      const langeValue = appStores.lang == "zh-Hans" ? "zh-Ug" : "zh-Hans";
      locale.value = langeValue;
      common_vendor.index.setStorageSync("language", langeValue);
      appStores.lang = langeValue;
    };
    common_vendor.onMounted(() => {
      const res = common_vendor.index.getWindowInfo();
      innerWidth.value = res.windowWidth - leftWidth.value - 25;
      const rect = common_vendor.index.getMenuButtonBoundingClientRect();
      innerHeight.value = rect.height + 6;
      innerTop.value = rect.top - statusBarH.value - 3;
      leftWidth.value = props.leftExtraWidth ? res.windowWidth - rect.left + props.leftExtraWidth - 48 : res.windowWidth - rect.left - 48;
      centerWidth.value = res.windowWidth - leftWidth.value - rect.width;
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.back || __props.changeLang
      }, __props.back || __props.changeLang ? common_vendor.e({
        b: __props.changeLang
      }, __props.changeLang ? {
        c: common_vendor.n(common_vendor.unref(appStores).lang == "zh-Ug" ? "lang-selected" : ""),
        d: common_vendor.n(common_vendor.unref(appStores).lang == "zh-Hans" ? "lang-selected" : ""),
        e: common_vendor.o(changeLanguage)
      } : {}, {
        f: __props.back
      }, __props.back ? {
        g: common_vendor.p({
          type: "left",
          size: "24",
          color: __props.color
        }),
        h: common_vendor.o(previous)
      } : {}) : {}, {
        i: leftWidth.value + "px",
        j: __props.title
      }, __props.title ? {
        k: common_vendor.t(__props.title),
        l: __props.color
      } : {}, {
        m: centerWidth.value + "px",
        n: innerHeight.value + "px",
        o: innerTop.value + "px",
        p: innerWidth.value + "px",
        q: __props.fixed ? 1 : "",
        r: common_vendor.s({
          height: navbarHeight.value
        }),
        s: common_vendor.s(_ctx.__cssVars())
      });
    };
  }
};
wx.createComponent(_sfc_main);
