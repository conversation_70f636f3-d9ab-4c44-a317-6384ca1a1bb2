/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.main-content.data-v-07e72d3c {
  /* 	flex: 1;
  padding: 24rpx 0 0 0; */
  overflow-y: auto;
  /* margin-top: 200rpx; */
  padding-top: 100px;
}
.scene-section.data-v-07e72d3c {
  width: 686rpx;
  height: 578rpx;
  padding: 24rpx 16rpx;
  margin: 0 20rpx 24rpx 20rpx;
}
.recommend-list.data-v-07e72d3c {
  margin-top: 30rpx;
  padding-bottom: 24rpx;
}
.recommend-name.data-v-07e72d3c {
  font-size: 44rpx;
  color: #727972;
  margin-bottom: 16rpx;
  margin-left: 18px;
}
.recommend-list-info.data-v-07e72d3c {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 14rpx;
  padding: 16rpx;
  /* 	direction: ltr;
  unicode-bidi: isolate; */
}
.recommend-item.data-v-07e72d3c {
  display: flex;
  border-radius: 22rpx;
  border: 1px solid #ece5f2;
  width: 330rpx;
  height: 115rpx;
}
.recommend-img.data-v-07e72d3c {
  width: 92rpx;
  height: 100rpx;
  border-radius: 16rpx;
  background: #eaeaea;
  margin: 4px;
}
.recommend-info.data-v-07e72d3c {
  margin-top: 8rpx;
}
.recommend-title.data-v-07e72d3c {
  font-size: 8px;
  font-weight: 600;
  line-height: 15px;
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.recommend-time.data-v-07e72d3c {
  font-size: 8px;
  color: #666;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100px;
}
.recommend-meta.data-v-07e72d3c {
  display: flex;
  align-items: center;
  font-size: 18rpx;
  color: #ffb400;
}
.recommend-meta .score.data-v-07e72d3c {
  color: #006397;
  font-weight: bold;
  margin-right: 4rpx;
}
.recommend-meta .count.data-v-07e72d3c {
  color: #888;
  margin-left: 4rpx;
}
.iconfont.data-v-07e72d3c {
  font-style: normal;
  font-size: 28rpx;
  color: #006397;
}