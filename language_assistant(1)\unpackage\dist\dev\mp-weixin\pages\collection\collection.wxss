/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-cd17183b {
  padding: 0 32rpx;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  padding-top: 100px;
}
.tabs-container.data-v-cd17183b {
  margin-top: 20rpx;
  padding: 0 20rpx;
}
.tabs-wrapper.data-v-cd17183b {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.tabs-wrapper-flex.data-v-cd17183b {
  display: flex;
  justify-content: center;
  align-items: center;
}
.tab-item.data-v-cd17183b {
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  background-color: #e5e5e5;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
  white-space: nowrap;
}
.tab-item.active.data-v-cd17183b {
  background-color: #007acc;
  color: #fff;
}
.content-container.data-v-cd17183b {
  padding: 20rpx;
}
.content-list.data-v-cd17183b {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.content-card.data-v-cd17183b {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.card-content.data-v-cd17183b {
  display: flex;
  gap: 20rpx;
}
.card-cover.data-v-cd17183b {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  background: #eaeaea;
  flex-shrink: 0;
}
.card-info.data-v-cd17183b {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.card-title.data-v-cd17183b {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
}
.card-subtitle.data-v-cd17183b {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.card-date.data-v-cd17183b {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
}
.card-rating.data-v-cd17183b {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.rating-stars.data-v-cd17183b {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.rating-score.data-v-cd17183b {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.stars.data-v-cd17183b {
  display: flex;
  gap: 2rpx;
}
.star.data-v-cd17183b {
  font-size: 24rpx;
  color: #ddd;
}
.star.filled.data-v-cd17183b {
  color: #ffa500;
}
.rating-count.data-v-cd17183b {
  font-size: 24rpx;
  color: #666;
}
.no-data.data-v-cd17183b {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  font-size: 28rpx;
  color: #666;
}
.load-more.data-v-cd17183b {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999;
}